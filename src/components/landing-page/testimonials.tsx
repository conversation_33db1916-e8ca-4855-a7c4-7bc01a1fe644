"use client";

import Image from "next/image";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Professional Model",
      image: "/testimonials/user1.jpg",
      quote: "The AI attractiveness score helped me understand my best angles and features. I've improved my portfolio and gained more modeling opportunities.",
    },
    {
      name: "<PERSON>",
      role: "Photographer",
      image: "/testimonials/user2.jpg",
      quote: "Using the AI looks rater with my clients has transformed my photography sessions. The objective analysis helps them feel more confident in front of the camera.",
    },
    {
      name: "<PERSON>",
      role: "Content Creator",
      image: "/testimonials/user3.jpg",
      quote: "The AI's detailed analysis of my facial features helped me develop a more confident on-camera presence. My engagement rates have doubled!",
    },
    {
      name: "<PERSON>",
      role: "Marketing Manager",
      image: "/testimonials/user4.jpg",
      quote: "Our team uses the attractiveness score to improve our professional image. It's helped us dress better and present ourselves more confidently in client meetings.",
    },
    {
      name: "<PERSON>",
      role: "HR Consultant",
      image: "/testimonials/user5.jpg",
      quote: "The AI's objective feedback on professional appearance has been invaluable for our career coaching programs. Participants love the actionable insights.",
    },
    {
      name: "<PERSON>",
      role: "Entrepreneur",
      image: "/testimonials/user6.jpg",
      quote: "The AI analysis helped me understand how to dress for success. My networking results have improved significantly since following its recommendations.",
    },
  ];

  return (
    <section className="py-24 bg-muted/50">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center mb-12">
          What Our Users Say
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-background p-6 rounded-lg shadow-lg flex flex-col items-center text-center"
            >
              <div className="relative w-20 h-20 mb-4">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  fill
                  className="rounded-full object-cover"
                />
              </div>
              <blockquote className="mb-4 text-muted-foreground">
                &ldquo;{testimonial.quote}&rdquo;
              </blockquote>
              <cite className="not-italic">
                <div className="font-semibold">{testimonial.name}</div>
                <div className="text-sm text-muted-foreground">
                  {testimonial.role}
                </div>
              </cite>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
