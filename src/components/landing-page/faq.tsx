import {
  Accordion,
  AccordionContent,
  Accordion<PERSON>tem,
  AccordionTrigger,
} from "../ui/accordion";

const Faq = () => {
  const faqs = [
    {
      question: "How attractive am I? What factors does the AI consider in the attractiveness analysis?",
      answer:
        "When you ask 'How attractive am I?', our AI analyzes multiple facial features including facial symmetry, golden ratio proportions, skin texture, facial harmony, and smile aesthetics. The attractiveness analysis considers universal beauty markers backed by scientific research while being sensitive to diverse beauty standards across different cultures.",
    },
    {
      question: "How does the face shape detector work and what shapes can it identify?",
      answer:
        "Our face shape detector uses advanced AI to analyze your facial structure and identify common face shapes including oval, round, square, heart, diamond, and oblong. The shape detector provides personalized styling recommendations based on your detected face shape to enhance your natural attractiveness.",
    },
    {
      question: "How long does it take to get my 'how attractive am I' score?",
      answer:
        "The 'how attractive am I' analysis is nearly instantaneous! Once you upload your photo, our AI processes it within seconds to provide you with a detailed attractiveness score breakdown and personalized recommendations. The face shape detector also works instantly alongside the attractiveness analysis.",
    },
    {
      question: "How does the AI attractiveness rating system work with the shape detector?",
      answer:
        "Our comprehensive system combines attractiveness analysis with face shape detection using advanced computer vision technology. When you ask 'how attractive am I?', the AI considers facial symmetry, proportions, and other scientifically studied attributes while the shape detector identifies your facial structure for personalized styling tips.",
    },
    {
      question: "Is the 'how attractive am I' rating system biased towards certain face shapes or ethnicities?",
      answer:
        "No, our 'how attractive am I' AI and face shape detector have been trained on diverse datasets representing people from all backgrounds. The system recognizes and appreciates beauty across different ethnicities, ages, and face shapes. We continuously update our models to ensure fair and unbiased analysis for everyone.",
    },
    {
      question: "How accurate are the 'how attractive am I' ratings and face shape detection?",
      answer:
        "Our 'how attractive am I' analysis and face shape detector provide consistent and objective results based on established beauty standards and facial geometry research. However, beauty is subjective, and our ratings should be viewed as one perspective among many. The goal is to provide insights for self-improvement while promoting healthy self-image.",
    },
    {
      question: "How can I improve my attractiveness score using the shape detector insights?",
      answer:
        "Our system provides personalized recommendations based on both your attractiveness analysis and face shape detection. These include tips about optimal photo angles, lighting, facial expressions, hairstyles that complement your face shape, and grooming suggestions tailored to enhance your natural features.",
    },
    {
      question: "Is my data private when I ask 'how attractive am I' or use the shape detector?",
      answer:
        "Yes, we take your privacy very seriously. Whether you're using our 'how attractive am I' analysis or face shape detector, your images are deleted immediately after analysis and never stored in our database. We never share your photos or personal data with third parties.",
    },
  ];

  return (
    <section id="faq" className="py-32">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center">
          Frequently Asked Questions
        </h2>
        {faqs.map((faq, index) => (
          <Accordion key={index} type="single" collapsible>
            <AccordionItem value={`item-${index}`}>
              <AccordionTrigger className="hover:text-foreground/60 hover:no-underline text-lg">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-normal">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        ))}
      </div>
    </section>
  );
};

export default Faq;
