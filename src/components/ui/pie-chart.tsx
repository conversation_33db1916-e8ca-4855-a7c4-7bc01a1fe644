import React from 'react';
import Image from 'next/image';

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation: string;
}

interface AttractivenessResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations: string;
}

interface AnalysisItem {
  type: string;
  overall_score?: number;
  factors?: Factor[];
  analysis_text?: string;
  overall_recommendations?: string;
  shape?: string;
  confidence_score?: number;
  measurements?: any;
  styling_tips?: any;
  celebrity_examples?: string[];
  description?: string;
}

interface MultiAnalysisResult {
  analyses: AnalysisItem[];
}

interface PieChartComponentProps {
  data: AttractivenessResult | MultiAnalysisResult | null;
  uploadedImage?: string;
}

const COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE'];

export default function AttractivenessPieChart({ data, uploadedImage }: PieChartComponentProps) {
  if (!data) return null;

  // Handle both single and multi-analysis formats
  const isSingleAnalysis = 'factors' in data;
  let currentData: AttractivenessResult;

  if (isSingleAnalysis) {
    currentData = data as AttractivenessResult;
  } else {
    // For multi-analysis, use the first analysis result (attractive-score) for the pie chart
    const multiData = data as MultiAnalysisResult;
    const attractiveScoreAnalysis = multiData.analyses.find(analysis => analysis.type === 'attractive-score');

    if (attractiveScoreAnalysis && attractiveScoreAnalysis.factors) {
      // Convert to AttractivenessResult format
      currentData = {
        overall_score: attractiveScoreAnalysis.overall_score || 0,
        factors: attractiveScoreAnalysis.factors,
        analysis_text: attractiveScoreAnalysis.analysis_text || '',
        overall_recommendations: attractiveScoreAnalysis.overall_recommendations || ''
      };
    } else {
      return <p>No attractiveness analysis data available</p>;
    }
  }



  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="grid md:grid-cols-2 gap-8 mb-6">
        {/* Left side - Score and Analysis Text */}
        <div className="text-center md:text-left">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Your Attractiveness Score: {currentData.overall_score}/10
          </h2>
          <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 mb-4">
            {Math.round(currentData.overall_score * 10)}%
          </div>
          <p className="text-lg text-gray-600">
            {currentData.analysis_text}
          </p>
        </div>

        {/* Right side - Uploaded Image */}
        {uploadedImage && (
          <div className="flex justify-center items-center">
            <div className="relative w-64 h-64 rounded-lg overflow-hidden shadow-md">
              <Image
                src={uploadedImage}
                alt="Uploaded photo"
                fill
                className="object-cover"
              />
            </div>
          </div>
        )}
      </div>

      {/* Detailed Analysis Section */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-gray-900">Detailed Analysis</h3>
        {currentData.factors.map((factor: Factor, index: number) => (
          <div key={factor.name} className="bg-gray-50 p-4 rounded-lg">
            {/* Progress bar on top */}
            <div className="mb-3">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium text-gray-900">{factor.name}</h4>
                <span className="text-sm font-semibold px-2 py-1 rounded text-white"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}>
                  {factor.score.toFixed(1)}/10
                </span>
              </div>
              {/* Progress bar */}
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${(factor.score / 10) * 100}%`,
                    backgroundColor: COLORS[index % COLORS.length]
                  }}
                />
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2">{factor.description}</p>
            <p className="text-sm text-blue-600 italic">{factor.recommendation}</p>
          </div>
        ))}

        <div className="mt-6 p-4 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Overall Recommendations</h4>
          <p className="text-gray-700">{currentData.overall_recommendations}</p>
        </div>
      </div>
    </div>
  );
}
