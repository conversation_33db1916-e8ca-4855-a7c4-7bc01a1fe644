import React from 'react';
import { Card } from './card';
import { Badge } from './badge';
import AttractivenessPieChart from './pie-chart';

interface Factor {
  name: string;
  score: number;
  description: string;
  recommendation: string;
}

interface AnalysisItem {
  type: string;
  overall_score?: number;
  factors?: Factor[];
  analysis_text?: string;
  overall_recommendations?: string;
  shape?: string;
  confidence_score?: number;
  measurements?: any;
  styling_tips?: any;
  celebrity_examples?: string[];
  description?: string;
}

interface MultiAnalysisResult {
  analyses: AnalysisItem[];
}

interface AttractivenessResult {
  overall_score: number;
  factors: Factor[];
  analysis_text: string;
  overall_recommendations: string;
}

interface MultiAnalysisResultsProps {
  data: AttractivenessResult | MultiAnalysisResult | null;
  uploadedImage?: string;
}

const getAnalysisTitle = (type: string): string => {
  const titles: { [key: string]: string } = {
    'attractive-score': 'Attractiveness Analysis',
    'face-shape-detector': 'Face Shape Analysis',
    'golden-ratio-face': 'Golden Ratio Analysis',
    'smile-rating': 'Smile Analysis'
  };
  return titles[type] || type;
};

const FaceShapeAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <h3 className="text-3xl font-bold text-gray-900 mb-6">Face Shape Analysis</h3>
    <div className="space-y-6">
      <div className="text-center">
        <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 mb-2">
          {analysis.shape}
        </div>
        {analysis.confidence_score && (
          <p className="text-lg text-gray-600">
            Confidence: {(analysis.confidence_score * 100).toFixed(1)}%
          </p>
        )}
      </div>

      {analysis.description && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Description:</h4>
          <p className="text-gray-600">{analysis.description}</p>
        </div>
      )}

      {analysis.styling_tips && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Styling Tips:</h4>
          <div className="space-y-3">
            {Object.entries(analysis.styling_tips).map(([key, value]) => (
              <div key={key} className="flex flex-col sm:flex-row sm:items-center gap-2">
                <span className="font-medium text-gray-900 capitalize min-w-0 sm:min-w-[120px]">
                  {key.replace('_', ' ')}:
                </span>
                <span className="text-gray-600 flex-1">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {analysis.celebrity_examples && analysis.celebrity_examples.length > 0 && (
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Celebrity Examples:</h4>
          <div className="flex flex-wrap gap-2">
            {analysis.celebrity_examples.map((celebrity, index) => (
              <span key={index} className="px-3 py-1 bg-white rounded-full text-sm font-medium text-gray-700 shadow-sm">
                {celebrity}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  </div>
);

const GoldenRatioAnalysis = ({ analysis }: { analysis: AnalysisItem }) => (
  <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
    <h3 className="text-3xl font-bold text-gray-900 mb-6">Golden Ratio Analysis</h3>
    <div className="space-y-6">
      {analysis.overall_score && (
        <div className="text-center">
          <div className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-purple-600 mb-2">
            {analysis.overall_score.toFixed(1)}/10
          </div>
          <p className="text-lg text-gray-600">Golden Ratio Score</p>
        </div>
      )}

      {analysis.analysis_text && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Analysis:</h4>
          <p className="text-gray-600 italic">"{analysis.analysis_text}"</p>
        </div>
      )}

      {analysis.measurements && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-3">Measurements:</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {Object.entries(analysis.measurements).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center p-2 bg-white rounded">
                <span className="capitalize text-gray-700">{key.replace('_', ' ')}:</span>
                <span className="font-medium text-gray-900">{value as string}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {analysis.overall_recommendations && (
        <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Recommendations:</h4>
          <p className="text-gray-700">{analysis.overall_recommendations}</p>
        </div>
      )}
    </div>
  </div>
);

export default function MultiAnalysisResults({ data, uploadedImage }: MultiAnalysisResultsProps) {
  if (!data) return null;

  // Handle single analysis (backward compatibility)
  const isSingleAnalysis = 'factors' in data;
  if (isSingleAnalysis) {
    return <AttractivenessPieChart data={data} uploadedImage={uploadedImage} />;
  }

  // Handle multi-analysis
  const multiData = data as MultiAnalysisResult;
  const analyses = multiData.analyses;

  // Check if analyses array exists and has items
  if (!analyses || !Array.isArray(analyses) || analyses.length === 0) {
    return <div>No analysis data available</div>;
  }

  return (
    <div className="space-y-8">
      {analyses.map((analysis, index) => {
        switch (analysis.type) {
          case 'attractive-score':
            // Convert to single analysis format for the pie chart
            const attractiveData: AttractivenessResult = {
              overall_score: analysis.overall_score || 0,
              factors: analysis.factors || [],
              analysis_text: analysis.analysis_text || '',
              overall_recommendations: analysis.overall_recommendations || ''
            };
            return (
              <div key={index}>
                <AttractivenessPieChart data={attractiveData} uploadedImage={uploadedImage} />
              </div>
            );
          
          case 'face-shape-detector':
            return <FaceShapeAnalysis key={index} analysis={analysis} />;
          
          case 'golden-ratio-face':
            return <GoldenRatioAnalysis key={index} analysis={analysis} />;
          
          default:
            return (
              <Card key={index} className="p-6">
                <h3 className="text-2xl font-bold mb-4">{getAnalysisTitle(analysis.type)}</h3>
                <div className="space-y-4">
                  {analysis.overall_score && (
                    <div className="text-center">
                      <div className="text-4xl font-bold text-primary mb-2">
                        {analysis.overall_score.toFixed(1)}/10
                      </div>
                    </div>
                  )}
                  {analysis.analysis_text && (
                    <p className="text-muted-foreground italic">"{analysis.analysis_text}"</p>
                  )}
                  {analysis.overall_recommendations && (
                    <div>
                      <h4 className="font-semibold mb-2">Recommendations:</h4>
                      <p className="text-muted-foreground">{analysis.overall_recommendations}</p>
                    </div>
                  )}
                </div>
              </Card>
            );
        }
      })}
    </div>
  );
}
